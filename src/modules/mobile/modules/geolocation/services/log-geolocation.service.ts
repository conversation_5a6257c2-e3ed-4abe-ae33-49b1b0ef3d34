import { Injectable, NotFoundException } from '@nestjs/common';
import { LogIntervalGeolocationDto } from '../dto/log-interval-geolocation.dto';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { DataSource, Repository } from 'typeorm';
import { LogGeolocation } from '../../../../../common/modules/database/entities/log-geolocation.entity';
import { v4 as uuidv4 } from 'uuid';
import { Geofence } from '../../../../../common/modules/database/entities/geofence.entity';
import { isPointInGeoJSONPolygon } from '../../../../../common/utils/geo.utils';
import { DeviceHeader } from '../../../../../common/decorators/current-device.decorator';
import { Device } from '../../../../../common/modules/database/entities/device.entity';
import { InjectRepository } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';

@Injectable()
export class LogGeolocationService {
  constructor(
    private readonly dataSource: DataSource,
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
    @InjectRepository(LogGeolocation)
    private readonly logGeolocationRepository: Repository<LogGeolocation>,
  ) {}

  async submitInterval(
    geolocationDto: LogIntervalGeolocationDto,
    user: User,
    deviceFromHeader: DeviceHeader,
  ) {
    const getGeofence = await this.dataSource
      .getRepository(Geofence)
      .createQueryBuilder('geofence')
      .leftJoinAndSelect('geofence.branch', 'branch')
      .leftJoinAndSelect('branch.timezone', 'timezone')
      .leftJoinAndSelect('geofence.zone', 'zone')
      .where('geofence.parent_branch_id = :parentBranchId', {
        parentBranchId: user.parent_branch_id,
      })
      .getMany();

    let selectedGeofence: Geofence | null = null;
    if (getGeofence.length > 0) {
      for (const geofence of getGeofence) {
        const geofenceData = geofence.geofence_data;
        if (geofenceData) {
          const isInside = isPointInGeoJSONPolygon(
            {
              latitude: geolocationDto.latitude,
              longitude: geolocationDto.longitude,
            },
            geofenceData,
          );
          if (isInside) {
            selectedGeofence = geofence;
            break;
          }
        }
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let device: Device | null = null;
      // Validate Device
      if (deviceFromHeader.device_uid) {
        const getDevice = await this.deviceRepository.findOne({
          where: {
            imei: deviceFromHeader.device_uid,
          },
        });
        device = getDevice;
      }

      if (!device) {
        throw new NotFoundException('Device not found');
      }

      const logGeolocation = new LogGeolocation();
      logGeolocation.uuid = uuidv4();
      logGeolocation.parent_branch_id = user.parent_branch_id;
      if (selectedGeofence) {
        console.log('selectedGeofence', selectedGeofence.active_time_start);
        logGeolocation.branch_id = selectedGeofence.branch_id;
        logGeolocation.branch_name = selectedGeofence.branch.branch_name;
        logGeolocation.geofence_id = selectedGeofence.id;
        logGeolocation.geofence_name = selectedGeofence.geofence_name;
        logGeolocation.zone_id = selectedGeofence.zone_id;
        logGeolocation.zone_name = selectedGeofence.zone.zone_name;

        logGeolocation.active_time_start = selectedGeofence.active_time_start;
        logGeolocation.active_time_end = selectedGeofence.active_time_end;
        logGeolocation.minimum_stay_duration =
          selectedGeofence.minimum_stay_duration;
        logGeolocation.maximum_stay_duration =
          selectedGeofence.maximum_stay_duration;
      } else {
        logGeolocation.branch_id = user.parent_branch_id;
        logGeolocation.branch_name = user.parent_branch.branch_name;
      }
      logGeolocation.role_id = user.role_id;
      logGeolocation.role_name = user.role.role_name;
      logGeolocation.user_id = user.id;
      logGeolocation.user_name = user.name;
      logGeolocation.latitude = geolocationDto.latitude;
      logGeolocation.longitude = geolocationDto.longitude;
      if (device) {
        logGeolocation.device_id = device.id;
        logGeolocation.device_name = device.device_name;
      }
      if (selectedGeofence) {
        logGeolocation.timezone_id = selectedGeofence.branch.timezone.id;
        logGeolocation.timezone_name =
          selectedGeofence.branch.timezone.timezone_name;
      } else {
        logGeolocation.timezone_id = user.parent_branch.timezone.id;
        logGeolocation.timezone_name =
          user.parent_branch.timezone.timezone_name;
      }
      logGeolocation.event_time = dayjs().toDate();
      logGeolocation.original_submitted_time = dayjs(
        geolocationDto.original_submitted_time,
      ).toDate();
      logGeolocation.geofence_data = {
        type: 'Point',
        coordinates: selectedGeofence?.geofence_data?.coordinates || [],
      };
      if (selectedGeofence) {
        // Determine session
        const session = await this.determineSession(
          user,
          device,
          selectedGeofence,
        );
        logGeolocation.geofence_session_uuid = session.uuid;
      }

      console.log(logGeolocation);

      // Save logGeolocation using queryRunner
      const savedLogGeolocationLog = await queryRunner.manager.save(
        LogGeolocation,
        logGeolocation,
      );

      await queryRunner.commitTransaction();

      return {
        message: 'Location processed successfully',
        data: geolocationDto,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async determineSession(
    user: User,
    device: Device,
    geofence: Geofence | null,
  ) {
    // Get one latest user geolocation log
    const latest = await this.logGeolocationRepository
      .createQueryBuilder('log_geolocation')
      .where('log_geolocation.user_id = :userId', { userId: user.id })
      .orderBy('log_geolocation.original_submitted_time', 'DESC')
      .limit(1)
      .getOne();

    let newUuid = uuidv4();

    if (!latest) {
      return {
        uuid: newUuid,
        new: true,
      };
    }

    // Compare device_id and geofence_id (similar to checkpoint's device_id and zone_id comparison)
    const latestDeviceId = latest.device_id?.toString();
    const currentDeviceId = device?.id?.toString();
    const latestGeofenceId = latest.geofence_id?.toString();
    const currentGeofenceId = geofence?.id?.toString();

    if (
      latestDeviceId !== currentDeviceId ||
      latestGeofenceId !== currentGeofenceId
    ) {
      return {
        uuid: newUuid,
        new: true,
      };
    }

    return {
      uuid: latest.geofence_session_uuid,
      new: false,
    };
  }
}
