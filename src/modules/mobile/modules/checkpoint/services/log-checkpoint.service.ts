import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  ApplyCheckpointDto,
  CheckpointType,
} from '../dto/apply-checkpoint.dto';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Checkpoint } from '../../../../../common/modules/database/entities/checkpoint.entity';
import { LogCheckpoint } from '../../../../../common/modules/database/entities/log-checkpoint.entity';
import { v4 as uuidv4 } from 'uuid';
import * as dayjs from 'dayjs';
import { DeviceHeader } from '../../../../../common/decorators/current-device.decorator';
import { Device } from '../../../../../common/modules/database/entities/device.entity';
import { LogCheckpointBatteryLevel } from '../../../../../common/modules/database/entities/log-checkpoint-battery-levels.entity';
import { AlertCommonService } from '../../../../../common/modules/alert/services/alert-common.service';

@Injectable()
export class LogCheckpointService {
  constructor(
    @InjectRepository(Checkpoint)
    private readonly checkpointRepository: Repository<Checkpoint>,
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
    @InjectRepository(LogCheckpoint)
    private readonly logCheckpointRepository: Repository<LogCheckpoint>,
    private dataSource: DataSource,
    private alertCommonService: AlertCommonService,
  ) {}

  /**
   * Apply a checkpoint scan from a user
   * Verifies device, checkpoint, and temporal constraints before creating log entries
   */
  async apply(
    data: ApplyCheckpointDto,
    user: User,
    deviceFromHeader: DeviceHeader,
  ) {
    const submittedTime = dayjs(data.original_submitted_time);
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate and retrieve device if provided
      const device = await this.validateAndGetDevice(deviceFromHeader);

      if (!device) {
        throw new NotFoundException('Device not found');
      }

      // Find and validate checkpoint
      const checkpointQueryBuiilder = this.checkpointRepository
        .createQueryBuilder('checkpoint')
        .leftJoinAndSelect('checkpoint.branch', 'branch')
        .leftJoinAndSelect('branch.timezone', 'timezone')
        .leftJoinAndSelect('checkpoint.parent_branch', 'parent_branch')
        .leftJoinAndSelect('checkpoint.zone', 'zone')
        .leftJoinAndSelect('checkpoint.checkpoint_type', 'checkpoint_type')
        .where('checkpoint.parent_branch_id = :branchId', {
          branchId: user.parent_branch_id,
        });

      if (data.type === CheckpointType.BEACON) {
        checkpointQueryBuiilder.andWhere(
          'checkpoint.major_value = :majorValue AND checkpoint.minor_value = :minorValue',
          {
            majorValue: data.beacon!.major_value,
            minorValue: data.beacon!.minor_value,
          },
        );
      } else if (data.type === CheckpointType.NFC) {
        checkpointQueryBuiilder.andWhere(
          'checkpoint.serial_number_hex = :serialNumber',
          {
            serialNumber: data.nfc!.hex,
          },
        );
      }

      const checkpoint = await checkpointQueryBuiilder.getOne();

      if (!checkpoint) {
        throw new NotFoundException('Checkpoint not found');
      }

      // Determine session
      const session = await this.determineSession(user, device, checkpoint);

      // Create checkpoint query builder for validation
      const logCheckpointQueryBuilder = this.createLogCheckpointQueryBuilder(
        user.parent_branch_id,
        checkpoint,
        user.id,
        device?.id,
      );

      // Validate time-based constraints
      await this.validateCheckpointAvailability(
        checkpoint,
        logCheckpointQueryBuilder,
        submittedTime,
      );

      // Create and save log entries
      const logCheckpoint = await this.createAndSaveLogCheckpoint(
        checkpoint,
        user,
        device,
        data,
        submittedTime,
        queryRunner,
        session.uuid,
      );

      // Save battery level information for beacon checkpoints
      if (data.type === CheckpointType.BEACON && data.beacon) {
        await this.createAndSaveLogCheckpointBatteryLevel(
          checkpoint,
          user,
          device,
          data,
          submittedTime,
          queryRunner,
        );

        //Update latest voltage in Checkpoint
        await this.checkpointRepository.update(checkpoint.id, {
          latest_voltage: data.beacon.battery_level,
        });
      }

      await queryRunner.commitTransaction();

      this.sendAlert(logCheckpoint).then().catch();

      return {
        message: 'Checkpoint processed successfully',
        data: logCheckpoint,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  public async sendAlert(logCheckpoint: LogCheckpoint) {
    return this.alertCommonService.processAlert({
      alertEventId: 5,
      logCheckpoint: logCheckpoint,
      checkpointId: logCheckpoint.checkpoint_id,
      zoneId: logCheckpoint.zone_id,
      userId: logCheckpoint.user_id,
      roleId: logCheckpoint.role_id,
      submittedDateTime: dayjs(
        logCheckpoint.original_submitted_time,
      ).toISOString(),
      deviceId: logCheckpoint.device_id,
      parentBranchId: logCheckpoint.parent_branch_id,
    });
  }

  async determineSession(user: User, device: Device, checkpoint: Checkpoint) {
    // Get one latest user scan checkpoint
    const latest = await this.logCheckpointRepository
      .createQueryBuilder('log_checkpoint')
      .where('log_checkpoint.user_id = :userId', { userId: user.id })
      .orderBy('log_checkpoint.original_submitted_time', 'DESC')
      .limit(1)
      .getOne();

    let newUuid = uuidv4();

    if (!latest) {
      return {
        uuid: newUuid,
        new: true,
      };
    }

    if (
      latest.device_id.toString() !== device.id.toString() ||
      latest.zone_id.toString() !== checkpoint.zone_id.toString()
    ) {
      return {
        uuid: newUuid,
        new: true,
      };
    }

    return {
      uuid: latest.zone_session_uuid,
      new: false,
    };
  }

  /**
   * Validate and retrieve device if a device UID is provided
   */
  private async validateAndGetDevice(
    deviceFromHeader: DeviceHeader,
  ): Promise<Device | null> {
    if (!deviceFromHeader.device_uid) {
      return null;
    }

    const device = await this.deviceRepository.findOne({
      where: {
        imei: deviceFromHeader.device_uid,
      },
    });

    if (!device) {
      throw new NotFoundException('Device not found');
    }

    return device;
  }

  /**
   * Create a query builder for log checkpoint lookup based on checkpoint type
   */
  private createLogCheckpointQueryBuilder(
    parentBranchId: number,
    checkpoint: Checkpoint,
    userId: number,
    deviceId?: number,
  ) {
    const logCheckpointQueryBuilder = this.logCheckpointRepository
      .createQueryBuilder('log_checkpoint')
      .where('log_checkpoint.parent_branch_id = :parentBranchId', {
        parentBranchId,
      });

    // Add checkpoint type specific conditions
    if ([2, 3].includes(checkpoint.checkpoint_type_id)) {
      logCheckpointQueryBuilder.andWhere(
        'log_checkpoint.serial_number = :serialNumber',
        {
          serialNumber: checkpoint.serial_number_hex,
        },
      );
    } else if (checkpoint.checkpoint_type_id === 1) {
      logCheckpointQueryBuilder.andWhere(
        'log_checkpoint.major_value = :majorValue AND log_checkpoint.minor_value = :minorValue',
        {
          majorValue: checkpoint.major_value,
          minorValue: checkpoint.minor_value,
        },
      );
    }

    logCheckpointQueryBuilder.andWhere('log_checkpoint.user_id = :userId', {
      userId,
    });

    logCheckpointQueryBuilder.andWhere('log_checkpoint.checkpoint_id = :checkpointId', {
      checkpointId: checkpoint.id,
    });

    if (deviceId) {
      logCheckpointQueryBuilder.andWhere(
        'log_checkpoint.device_id = :deviceId',
        {
          deviceId,
        },
      );
    }

    return logCheckpointQueryBuilder;
  }

  /**
   * Validate checkpoint availability based on time constraints and day-specific rules
   */
  private async validateCheckpointAvailability(
    checkpoint: Checkpoint,
    logCheckpointQueryBuilder: ReturnType<
      typeof this.createLogCheckpointQueryBuilder
    >,
    submittedTime: dayjs.Dayjs,
  ) {
    // Check time interval between scans
    const latestLogCheckpoint = await logCheckpointQueryBuilder
      .orderBy('log_checkpoint.original_submitted_time', 'DESC')
      .limit(1)
      .getOne();

    if (latestLogCheckpoint) {
      const lastSubmittedTime = dayjs(
        latestLogCheckpoint.original_submitted_time,
      );
      const timeDifference = dayjs(submittedTime).diff(
        lastSubmittedTime,
        'minutes',
      );

      if (
        timeDifference < checkpoint.rotation_interval &&
        checkpoint.visit_interval
      ) {
        throw new BadRequestException(
          `Checkpoint must be scanned every ${checkpoint.rotation_interval} minutes`,
        );
      }

      // Check if daily scan limit has been reached
      // await this.validateDailyScanLimit(
      //   checkpoint,
      //   logCheckpointQueryBuilder,
      //   submittedTime,
      // );
    }

    // Validate checkpoint is active on the current day
    // this.validateCheckpointActiveDay(checkpoint, submittedTime);
  }

  /**
   * Validate if the checkpoint can be scanned on the current day
   */
  private validateCheckpointActiveDay(
    checkpoint: Checkpoint,
    submittedTime: dayjs.Dayjs,
  ) {
    // Map day of week to checkpoint configuration
    const dayConfig = {
      0: {
        active: checkpoint.sunday,
        count: checkpoint.sunday_count,
        name: 'Sunday',
      },
      1: {
        active: checkpoint.monday,
        count: checkpoint.monday_count,
        name: 'Monday',
      },
      2: {
        active: checkpoint.tuesday,
        count: checkpoint.tuesday_count,
        name: 'Tuesday',
      },
      3: {
        active: checkpoint.wednesday,
        count: checkpoint.wednesday_count,
        name: 'Wednesday',
      },
      4: {
        active: checkpoint.thursday,
        count: checkpoint.thursday_count,
        name: 'Thursday',
      },
      5: {
        active: checkpoint.friday,
        count: checkpoint.friday_count,
        name: 'Friday',
      },
      6: {
        active: checkpoint.saturday,
        count: checkpoint.saturday_count,
        name: 'Saturday',
      },
    };

    const day = submittedTime.day();
    const config = dayConfig[day];

    if (!config.active || config.count === 0) {
      throw new BadRequestException(
        `Checkpoint is not active on ${config.name}`,
      );
    }
  }

  /**
   * Validate if the checkpoint has reached its daily scan limit
   */
  private async validateDailyScanLimit(
    checkpoint: Checkpoint,
    logCheckpointQueryBuilder: ReturnType<
      typeof this.createLogCheckpointQueryBuilder
    >,
    submittedTime: dayjs.Dayjs,
  ) {
    const todayCheckpointCount = await logCheckpointQueryBuilder
      .andWhere(
        'log_checkpoint.original_submitted_time >= :startOfDay AND log_checkpoint.original_submitted_time <= :endOfDay',
        {
          startOfDay: submittedTime.startOf('day').toDate(),
          endOfDay: submittedTime.endOf('day').toDate(),
        },
      )
      .getCount();

    const day = submittedTime.day();

    // Map day of week to maximum count check
    const dayMaxCountMap = {
      0: { active: checkpoint.sunday, max: checkpoint.sunday_count },
      1: { active: checkpoint.monday, max: checkpoint.monday_count },
      2: { active: checkpoint.tuesday, max: checkpoint.tuesday_count },
      3: { active: checkpoint.wednesday, max: checkpoint.wednesday_count },
      4: { active: checkpoint.thursday, max: checkpoint.thursday_count },
      5: { active: checkpoint.friday, max: checkpoint.friday_count },
      6: { active: checkpoint.saturday, max: checkpoint.saturday_count },
    };

    const config = dayMaxCountMap[day];

    if (config.active && todayCheckpointCount >= config.max) {
      throw new BadRequestException(
        `Checkpoint has reached the maximum scan count for today.`,
      );
    }
  }

  /**
   * Create and save a log checkpoint entry
   */
  private async createAndSaveLogCheckpoint(
    checkpoint: Checkpoint,
    user: User,
    device: Device | null,
    data: ApplyCheckpointDto,
    submittedTime: dayjs.Dayjs,
    queryRunner: import('typeorm').QueryRunner,
    zoneSessionUuid: string,
  ): Promise<LogCheckpoint> {
    const logCheckpoint = new LogCheckpoint();
    logCheckpoint.uuid = uuidv4();
    logCheckpoint.parent_branch_id = checkpoint.parent_branch_id;
    logCheckpoint.branch_id = checkpoint.branch_id;
    logCheckpoint.branch_name = checkpoint.branch.branch_name;
    logCheckpoint.role_id = user.role_id;
    logCheckpoint.role_name = user.role.role_name;
    logCheckpoint.user_id = user.id;
    logCheckpoint.user_name = user.name;
    logCheckpoint.timezone_id = checkpoint.branch.timezone.id;
    logCheckpoint.timezone_name = checkpoint.branch.timezone.timezone_name;
    logCheckpoint.latitude = data.latitude;
    logCheckpoint.longitude = data.longitude;
    logCheckpoint.checkpoint_id = checkpoint.id;
    logCheckpoint.checkpoint_name = checkpoint.checkpoint_name;
    logCheckpoint.zone_id = checkpoint.zone_id;
    logCheckpoint.zone_name = checkpoint.zone.zone_name;
    logCheckpoint.is_beacon =
      parseInt(checkpoint.checkpoint_type_id as any) === 1;
    logCheckpoint.major_value = checkpoint.major_value;
    logCheckpoint.minor_value = checkpoint.minor_value;
    logCheckpoint.checkpoint_type_id = checkpoint.checkpoint_type_id;
    logCheckpoint.serial_number = checkpoint.serial_number_hex;

    if (device) {
      logCheckpoint.device_id = device.id;
      logCheckpoint.device_name = device.device_name;
    }

    logCheckpoint.original_submitted_time = submittedTime.toDate();
    logCheckpoint.event_time = dayjs().toDate();
    logCheckpoint.zone_session_uuid = zoneSessionUuid;

    await queryRunner.manager.save(LogCheckpoint, logCheckpoint);

    return logCheckpoint;
  }

  /**
   * Create and save a log checkpoint battery level entry
   */
  private async createAndSaveLogCheckpointBatteryLevel(
    checkpoint: Checkpoint,
    user: User,
    device: Device | null,
    data: ApplyCheckpointDto,
    submittedTime: dayjs.Dayjs,
    queryRunner: import('typeorm').QueryRunner,
  ): Promise<LogCheckpointBatteryLevel | null> {
    // Only save battery level for beacon checkpoints
    if (data.type !== CheckpointType.BEACON || !data.beacon) {
      return null;
    }

    const logBatteryLevel = new LogCheckpointBatteryLevel();
    logBatteryLevel.uuid = uuidv4();
    logBatteryLevel.parent_branch_id = checkpoint.parent_branch_id;
    logBatteryLevel.branch_id = checkpoint.branch_id;
    logBatteryLevel.branch_name = checkpoint.branch.branch_name;
    logBatteryLevel.timezone_id = checkpoint.branch.timezone.id;
    logBatteryLevel.timezone_name = checkpoint.branch.timezone.timezone_name;
    logBatteryLevel.checkpoint_id = checkpoint.id;
    logBatteryLevel.checkpoint_name = checkpoint.checkpoint_name;
    logBatteryLevel.voltage = data.beacon.battery_level;

    if (device) {
      logBatteryLevel.device_id = device.id;
      logBatteryLevel.device_name = device.device_name;
    }

    logBatteryLevel.original_submitted_time = submittedTime.toDate();
    logBatteryLevel.event_time = dayjs().toDate();

    await queryRunner.manager.save(LogCheckpointBatteryLevel, logBatteryLevel);

    return logBatteryLevel;
  }
}
