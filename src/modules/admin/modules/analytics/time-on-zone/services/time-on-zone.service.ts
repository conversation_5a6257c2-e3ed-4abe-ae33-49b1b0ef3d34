import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { GetTimeOnZoneDto } from '../dto/get-time-on-zone.dto';
import { User } from '../../../../../../common/modules/database/entities/user.entity';
import { Branch } from '../../../../../../common/modules/database/entities/branch.entity';
import { OrderDirection } from '../../../../../../common/dto/base-query.dto';
import { ReportFormatType } from '../../../../../../common/interfaces/report-format-type.enum';
import { ReportFileSave } from '../../../../../../common/interfaces/report-file-save.enum';
import { StorageService } from '../../../../../../common/modules/storage/services/storage.service';
import {
  TimeOnZoneFilters,
  TimeOnZoneGenerateDocumentService,
} from '../../../../../../common/modules/analytics/time-on-zone/services/time-on-zone-generate-document.service';
import { Device } from '../../../../../../common/modules/database/entities/device.entity';
import { Label } from '../../../../../../common/modules/database/entities/label.entity';
import { TimeOnZoneAnalyticService } from '../../../../../../common/modules/analytics/time-on-zone/services/time-on-zone-analytic.service';
import { Zone } from '../../../../../../common/modules/database/entities/zone.entity';
import { Timezone } from '../../../../../../common/modules/database/entities/timezone.entity';

@Injectable()
export class TimeOnZoneService {
  constructor(
    private readonly storageService: StorageService,
    @InjectRepository(Branch)
    private readonly branchRepository: Repository<Branch>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
    @InjectRepository(Label)
    private readonly labelRepository: Repository<Label>,
    @InjectRepository(Zone)
    private readonly zoneRepository: Repository<Zone>,
    @InjectRepository(Timezone)
    private readonly timezoneRepository: Repository<Timezone>,
    private readonly timeOnZoneGenerateDocumentService: TimeOnZoneGenerateDocumentService,
    private readonly timeOnZoneAnalyticService: TimeOnZoneAnalyticService,
  ) {}

  async findAll(query: GetTimeOnZoneDto, user: User, branch: Branch) {
    // Extract and set default values for query parameters
    let {
      page = 1,
      limit = 10,
      order_by = 'entry',
      order_direction = OrderDirection.ASC,
      start_date,
      end_date,
      start_time = '00:00:00',
      end_time = '23:59:59',
      branch_id,
      zone_id,
      zone_labels,
      user_id,
      user_labels,
      device_id,
      device_labels,
    } = query;

    const timezone = await this.timezoneRepository.findOne({
      where: {
        id: branch.timezone_id,
      },
    });

    if (!timezone) {
      throw new Error('Timezone not found');
    }

    const timeOnZoneData = await this.timeOnZoneAnalyticService.getTimeOnZone(
      branch.parent_id,
      {
        startDate: start_date,
        endDate: end_date,
        startTime: start_time,
        endTime: end_time,
        branchId: branch_id,
        userId: user_id,
        userLabels: user_labels,
        deviceId: device_id,
        deviceLabels: device_labels,
        zoneId: zone_id,
        zoneLabels: zone_labels,
        orderBy: order_by,
        orderDirection: order_direction,
        limit: limit,
        page: page,
      },
      {
        timezone: timezone,
        user: user,
      },
    );

    return {
      data: timeOnZoneData.data,
      meta: {
        total: timeOnZoneData.total,
        page: page,
        limit: limit,
        total_pages: Math.ceil(timeOnZoneData.total / limit),
      },
    };
  }

  /**
   * Exports time on zone logs as PDF or spreadsheet
   *
   * @param type - Export file type (pdf/spreadsheet)
   * @param query - DTO containing all filter parameters
   * @param user - Current authenticated user
   * @param branch - Current selected branch
   * @param saveFileType - Response type (json/buffer)
   * @returns Object containing public URL and filename of the generated report
   * @throws {Error} If there's an issue generating the document
   *
   * @example
   * const { url, filename } = await service.exportTimeOnZoneLogs(
   *   ReportFormatType.PDF,
   *   query,
   *   user,
   *   branch,
   *   ReportFileSave.LINK
   * );
   */
  async exportTimeOnZoneLogs(
    type: ReportFormatType,
    query: GetTimeOnZoneDto,
    user: User,
    branch: Branch,
    saveFileType: ReportFileSave,
  ) {
    // Get the data using existing findAll logic but without pagination
    const modifiedQuery = {
      ...query,
      page: 1,
      limit: 1000, // Set a reasonable limit for export
    };

    const { data } = await this.findAll(modifiedQuery, user, branch);

    // Get timezone for document generation
    const timezone = await this.timezoneRepository.findOne({
      where: {
        id: branch.timezone_id,
      },
    });

    if (!timezone) {
      throw new Error('Timezone not found');
    }

    const filters: TimeOnZoneFilters = {
      startDate: query.start_date || null,
      endDate: query.end_date || null,
      startTime: query.start_time || null,
      endTime: query.end_time || null,
      branch: null,
      zone: null,
      zone_labels: [],
      user: null,
      user_labels: [],
      device: null,
      device_labels: [],
    };

    if (query.branch_id) {
      const branch = await this.branchRepository.findOne({
        where: { id: query.branch_id },
      });
      if (branch) {
        filters.branch = branch;
      }
    }

    if (query.zone_id) {
      const zone = await this.zoneRepository.findOne({
        where: { id: query.zone_id },
      });
      if (zone) {
        filters.zone = zone;
      }
    } else if (query.zone_labels && query.zone_labels.length > 0) {
      const zoneLabels = await this.labelRepository.find({
        where: { id: In(query.zone_labels) },
      });
      if (zoneLabels) {
        filters.zone_labels = zoneLabels;
      }
    }

    if (query.user_id) {
      const user = await this.userRepository.findOne({
        where: { id: query.user_id },
      });
      if (user) {
        filters.user = user;
      }
    } else if (query.user_labels && query.user_labels.length > 0) {
      const userLabels = await this.labelRepository.find({
        where: { id: In(query.user_labels) },
      });
      if (userLabels) {
        filters.user_labels = userLabels;
      }
    }

    if (query.device_id) {
      const device = await this.deviceRepository.findOne({
        where: { id: query.device_id },
      });
      if (device) {
        filters.device = device;
      }
    } else if (query.device_labels && query.device_labels.length > 0) {
      const deviceLabels = await this.labelRepository.find({
        where: { id: In(query.device_labels) },
      });
      if (deviceLabels) {
        filters.device_labels = deviceLabels;
      }
    }

    if (type === ReportFormatType.PDF) {
      const { buffer, filename } =
        await this.timeOnZoneGenerateDocumentService.generatePDF(data, filters, timezone);

      if (saveFileType === ReportFileSave.LINK) {
        // Upload the PDF to storage
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/pdf',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    } else if (type === ReportFormatType.SPREADSHEET) {
      const { buffer, filename } =
        await this.timeOnZoneGenerateDocumentService.generateSpreadsheet(
          data,
          filters,
          timezone,
        );

      if (saveFileType === ReportFileSave.LINK) {
        // Upload the spreadsheet to storage
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    }

    throw new Error(`Unsupported export type: ${type}`);
  }
}
