import {
  <PERSON><PERSON><PERSON>,
  <PERSON>reateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Branch } from './branch.entity';
import { Role } from './role.entity';
import { User } from './user.entity';
import { Device } from './device.entity';
import { Timezone } from './timezone.entity';
import { Checkpoint } from './checkpoint.entity';
import { Zone } from './zone.entity';
import { Geofence } from './geofence.entity';

@Entity({ name: 'log_geolocations', schema: 'public' })
export class LogGeolocation {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ type: 'bigint', nullable: true })
  uuid: string;

  @Column({ name: 'parent_branch_id', type: 'bigint', nullable: true })
  parent_branch_id: number;

  @ManyToOne(() => Branch)
  @JoinColumn({ name: 'parent_branch_id' })
  parent_branch: Branch;

  @Column({ name: 'branch_id', type: 'bigint', nullable: true })
  branch_id: number;

  @ManyToOne(() => Branch)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

  @Column({ name: 'branch_name', nullable: true })
  branch_name: string;

  @Column({ name: 'role_id', type: 'bigint', nullable: true })
  role_id: number;

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Column({ name: 'role_name', nullable: true })
  role_name: string;

  @Column({ name: 'user_id', type: 'bigint', nullable: true })
  user_id: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'user_name', nullable: true })
  user_name: string;

  @Column({ name: 'device_id', type: 'bigint', nullable: true })
  device_id: number;

  @ManyToOne(() => Device)
  @JoinColumn({ name: 'device_id' })
  device: Device;

  @Column({ name: 'device_name', nullable: true })
  device_name: string;

  @Column({ name: 'timezone_id', type: 'bigint', nullable: true })
  timezone_id: number;

  @ManyToOne(() => Timezone)
  @JoinColumn({ name: 'timezone_id' })
  timezone: Timezone;

  @Column({ name: 'timezone_name', nullable: true })
  timezone_name: string;

  @Column({ type: 'double precision', nullable: true })
  latitude: number;

  @Column({ type: 'double precision', nullable: true })
  longitude: number;

  @Column({ name: 'checkpoint_id', type: 'bigint', nullable: true })
  checkpoint_id: number;

  @ManyToOne(() => Checkpoint)
  @JoinColumn({ name: 'checkpoint_id' })
  checkpoint: Checkpoint;

  @Column({ name: 'checkpoint_name', nullable: true })
  checkpoint_name: string;

  @Column({ name: 'zone_id', type: 'bigint', nullable: true })
  zone_id: number | null;

  @ManyToOne(() => Zone)
  @JoinColumn({ name: 'zone_id' })
  zone: Zone;

  @Column({ name: 'zone_name', nullable: true })
  zone_name: string;

  @Column({ name: 'gps_tracking_enabled', type: 'boolean', nullable: true })
  gps_tracking_enabled: boolean;

  @Column({ name: 'gps_interval', type: 'integer', nullable: true })
  gps_interval: number;

  @Column({ name: 'geofence_id', type: 'bigint', nullable: true })
  geofence_id: number;

  @ManyToOne(() => Geofence)
  @JoinColumn({ name: 'geofence_id' })
  geofence: Geofence;

  @Column({ name: 'geofence_name', nullable: true })
  geofence_name: string;

  @Column({ name: 'active_time_start', type: 'time', nullable: true })
  active_time_start: string | null;

  @Column({ name: 'active_time_end', type: 'time', nullable: true })
  active_time_end: string | null;

  @Column({ name: 'minimum_stay_duration', type: 'time', nullable: true })
  minimum_stay_duration: string | null;

  @Column({ name: 'maximum_stay_duration', type: 'time', nullable: true })
  maximum_stay_duration: string | null;

  @Column({ name: 'geofence_data', type: 'json', nullable: true })
  geofence_data: any;

  @Column({
    name: 'original_submitted_time',
    type: 'timestamp with time zone',
    nullable: true,
  })
  original_submitted_time: Date;

  @CreateDateColumn({
    name: 'event_time',
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
  })
  event_time: Date;

  @Column({ name: 'geofence_session_uuid', nullable: true, type: 'uuid' })
  geofence_session_uuid: string;
}
