import {
  <PERSON><PERSON><PERSON>,
  <PERSON>reateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Branch } from './branch.entity';
import { Role } from './role.entity';
import { User } from './user.entity';
import { Device } from './device.entity';
import { Timezone } from './timezone.entity';
import { Checkpoint } from './checkpoint.entity';
import { Zone } from './zone.entity';
import { CheckpointType } from './checkpoint-type.entity';

@Entity({ name: 'log_checkpoints', schema: 'public' })
export class LogCheckpoint {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ type: 'uuid', nullable: true })
  uuid: string;

  @Column({ name: 'parent_branch_id', type: 'bigint', nullable: true })
  parent_branch_id: number;

  @ManyToOne(() => Branch)
  @JoinColumn({ name: 'parent_branch_id' })
  parent_branch: Branch;

  @Column({ name: 'branch_id', type: 'bigint', nullable: true })
  branch_id: number;

  @ManyToOne(() => Branch)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

  @Column({ name: 'branch_name', nullable: true })
  branch_name: string;

  @Column({ name: 'role_id', type: 'bigint', nullable: true })
  role_id: number;

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Column({ name: 'role_name', nullable: true })
  role_name: string;

  @Column({ name: 'user_id', type: 'bigint', nullable: true })
  user_id: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'user_name', nullable: true })
  user_name: string;

  @Column({ name: 'device_id', type: 'bigint', nullable: true })
  device_id: number;

  @ManyToOne(() => Device)
  @JoinColumn({ name: 'device_id' })
  device: Device;

  @Column({ name: 'device_name', nullable: true })
  device_name: string;

  @Column({ name: 'timezone_id', type: 'bigint', nullable: true })
  timezone_id: number;

  @ManyToOne(() => Timezone)
  @JoinColumn({ name: 'timezone_id' })
  timezone: Timezone;

  @Column({ name: 'timezone_name', nullable: true })
  timezone_name: string;

  @Column({ type: 'double precision', nullable: true })
  latitude: number;

  @Column({ type: 'double precision', nullable: true })
  longitude: number;

  @Column({ name: 'checkpoint_id', type: 'bigint', nullable: true })
  checkpoint_id: number;

  @ManyToOne(() => Checkpoint)
  @JoinColumn({ name: 'checkpoint_id' })
  checkpoint: Checkpoint;

  @Column({ name: 'checkpoint_name', nullable: true })
  checkpoint_name: string;

  @Column({ name: 'zone_id', type: 'bigint', nullable: true })
  zone_id: number;

  @ManyToOne(() => Zone)
  @JoinColumn({ name: 'zone_id' })
  zone: Zone;

  @Column({ name: 'zone_name', nullable: true })
  zone_name: string;

  @Column({ name: 'is_required_day', type: 'boolean', nullable: true })
  is_required_day: boolean;

  @Column({ name: 'target_scan_count', type: 'integer', nullable: true })
  target_scan_count: number;

  @Column({ name: 'is_beacon', type: 'boolean', nullable: true })
  is_beacon: boolean;

  @Column({ name: 'major_value', type: 'bigint', nullable: true })
  major_value: number;

  @Column({ name: 'minor_value', type: 'bigint', nullable: true })
  minor_value: number;

  @Column({ name: 'checkpoint_type_id', type: 'bigint', nullable: true })
  checkpoint_type_id: number;

  @ManyToOne(() => CheckpointType)
  @JoinColumn({ name: 'checkpoint_type_id' })
  checkpoint_type: CheckpointType;

  @Column({ name: 'serial_number', nullable: true })
  serial_number: string;

  @Column({
    name: 'original_submitted_time',
    type: 'timestamp with time zone',
    nullable: true,
  })
  original_submitted_time: Date;

  @CreateDateColumn({
    name: 'event_time',
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
  })
  event_time: Date;

  @Column({ name: 'zone_session_uuid', nullable: true, type: 'uuid' })
  zone_session_uuid: string;

}
