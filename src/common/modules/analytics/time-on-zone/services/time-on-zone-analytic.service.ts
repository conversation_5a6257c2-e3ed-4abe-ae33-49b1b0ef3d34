import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { LogCheckpoint } from '../../../database/entities/log-checkpoint.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { User } from '../../../database/entities/user.entity';
import { Branch } from '../../../database/entities/branch.entity';
import { Zone } from '../../../database/entities/zone.entity';
import { Device } from '../../../database/entities/device.entity';
import { OrderDirection } from '../../../../dto/base-query.dto';
import { parseTimeDaily } from '../../../../utils/time-utils';
import {
  TimeOnZoneAnalyticParams,
  TimeOnZoneData,
} from '../interfaces/time-on-zone-analytic.interface';
import * as dayjs from 'dayjs';

@Injectable()
export class TimeOnZoneAnalyticService {
  constructor(
    @InjectRepository(LogCheckpoint)
    private readonly logCheckpointRepository: Repository<LogCheckpoint>,
    @InjectRepository(Branch)
    private readonly branchRepository: Repository<Branch>,
    @InjectRepository(Zone)
    private readonly zoneRepository: Repository<Zone>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
  ) {}

  /**
   * Retrieves time spent in zones with detailed analytics
   *
   * @param parent_branch_id Parent branch identifier
   * @param params Filter parameters including date range, branch, zone, user, device filters
   * @param options Additional context like timezone and user info
   * @returns Object with time on zone data and total count for pagination
   */
  async getTimeOnZone(
    parent_branch_id: string | number,
    params: TimeOnZoneAnalyticParams,
    options: {
      timezone?: Timezone;
      user?: User;
    },
  ) {
    // Extract query parameters with defaults
    const {
      startDate,
      endDate,
      startTime = '00:00:00',
      endTime = '23:59:59',
      branchId,
      zoneId,
      zoneLabels,
      userId,
      userLabels,
      deviceId,
      deviceLabels,
      page = 1,
      limit = 10,
      orderBy,
      orderDirection = OrderDirection.ASC,
    } = params;

    // Validate timezone
    const timezone = options.timezone;
    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    // Parse date/time filters with timezone offset
    const rangeDateAndTimeDaily = parseTimeDaily({
      startDate: startDate,
      endDate: endDate,
      startTime: startTime,
      endTime: endTime,
      offset: timezone.gmt_offset,
    });

    // Build subquery for WITH clause to group by zone_session_uuid
    const sessionsSubquery = this.logCheckpointRepository
      .createQueryBuilder('log_checkpoint')
      .select([
        'log_checkpoint.user_id AS log_checkpoint_user_id',
        'log_checkpoint.device_id AS log_checkpoint_device_id',
        'log_checkpoint.zone_id AS log_checkpoint_zone_id',
        'MIN(log_checkpoint.original_submitted_time) as entry',
        'MAX(log_checkpoint.original_submitted_time) as exit',
      ])
      .where('log_checkpoint.parent_branch_id = :parent_branch_id', {
        parent_branch_id,
      })
      // Apply date range filters using brackets for multiple date ranges
      .andWhere(
        new Brackets(qb => {
          for (let i = 0; i < rangeDateAndTimeDaily.length; i++) {
            qb.orWhere(
              `log_checkpoint.original_submitted_time >= :startDate${i}::timestamptz AND log_checkpoint.original_submitted_time <= :endDate${i}::timestamptz`,
              {
                [`startDate${i}`]:
                  rangeDateAndTimeDaily[i].utc.startDate + '+00',
                [`endDate${i}`]: rangeDateAndTimeDaily[i].utc.endDate + '+00',
              },
            );
          }
        }),
      );

    // Apply branch filter if provided
    if (branchId) {
      sessionsSubquery.andWhere('log_checkpoint.branch_id = :branchId', {
        branchId,
      });
    }

    // Apply zone filters (direct ID or by labels)
    if (zoneId) {
      sessionsSubquery.andWhere('log_checkpoint.zone_id = :zoneId', { zoneId });
    } else if (zoneLabels) {
      sessionsSubquery
        .leftJoin('zone_labels', 'zl', 'zl.zone_id = log_checkpoint.zone_id')
        .andWhere('zl.label_id IN (:...zoneLabels)', {
          zoneLabels,
        });
    }

    // Apply user filters (direct ID or by labels)
    if (userId) {
      sessionsSubquery.andWhere('log_checkpoint.user_id = :userId', { userId });
    } else if (userLabels) {
      sessionsSubquery
        .leftJoin('user_labels', 'ul', 'ul.user_id = log_checkpoint.user_id')
        .andWhere('ul.label_id IN (:...userLabels)', {
          userLabels,
        });
    }

    // Apply device filters (direct ID or by labels)
    if (deviceId) {
      sessionsSubquery.andWhere('log_checkpoint.device_id = :deviceId', {
        deviceId,
      });
    } else if (deviceLabels) {
      sessionsSubquery
        .leftJoin(
          'device_labels',
          'dl',
          'dl.device_id = log_checkpoint.device_id',
        )
        .andWhere('dl.label_id IN (:...deviceLabels)', {
          deviceLabels,
        });
    }

    // Group by zone_session_uuid to get entry/exit pairs
    sessionsSubquery.groupBy(
      'log_checkpoint.zone_session_uuid, log_checkpoint.zone_id, log_checkpoint.device_id, log_checkpoint.user_id',
    );

    // Apply Ordering
    sessionsSubquery.orderBy(`${orderBy}`, orderDirection);

    const total = await sessionsSubquery.getCount();

    // Apply pagination
    if (page && limit) {
      sessionsSubquery.offset((page - 1) * limit).limit(limit);
    }

    // Execute the query
    const results = await sessionsSubquery.getRawMany();

    if (results.length === 0) {
      return {
        data: [],
        total: 0,
      };
    }

    const zoneIds = results.map((item: any) => item.log_checkpoint_zone_id);
    const deviceIds = results.map((item: any) => item.log_checkpoint_device_id);
    const userIds = results.map((item: any) => item.log_checkpoint_user_id);

    const getZones = await this.zoneRepository
      .createQueryBuilder('zone')
      .where('zone.id IN (:...zoneIds)', { zoneIds })
      .leftJoinAndSelect('zone.branch', 'branch')
      .leftJoinAndSelect('branch.timezone', 'timezone')
      .getMany();

    const getDevices = await this.deviceRepository
      .createQueryBuilder('device')
      .where('device.id IN (:...deviceIds)', { deviceIds })
      .getMany();

    const getUsers = await this.userRepository
      .createQueryBuilder('user')
      .where('user.id IN (:...userIds)', { userIds })
      .getMany();

    // Transform raw results to structured data objects
    const data: TimeOnZoneData[] = results.map(item => {
      const zone = getZones.find(
        (zone: any) => zone.id.toString() === item.log_checkpoint_zone_id,
      );
      const device = getDevices.find(
        (device: any) => device.id.toString() === item.log_checkpoint_device_id,
      );
      const user = getUsers.find(
        (user: any) => user.id.toString() === item.log_checkpoint_user_id,
      );
      const entry = dayjs(item.entry);
      const exit = dayjs(item.exit);

      return {
        zone_id: item.log_checkpoint_zone_id,
        zone_name: zone?.zone_name || 'Unknown Zone',
        user_id: item.log_checkpoint_user_id,
        user_name: user?.name || 'Unknown User',
        device_id: item.log_checkpoint_device_id,
        device_name: device?.device_name || 'Unknown Device',
        branch_id: zone?.branch?.id || 0,
        branch_name: zone?.branch?.branch_name || 'Unknown Branch',
        branch_code: zone?.branch?.branch_code || 'Unknown Branch',
        timezone_id: zone?.branch?.timezone?.id || 0,
        timezone_name:
          zone?.branch?.timezone?.timezone_name || 'Unknown Timezone',
        original_submitted_time: entry.toISOString(),
        entry: entry.toISOString(),
        exit: exit.toISOString(),
        duration: exit.diff(entry, 'seconds'),
      };
    });

    // Return paginated data with total count
    return {
      data,
      total,
    };
  }
}
