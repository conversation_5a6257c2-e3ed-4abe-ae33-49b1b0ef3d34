import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { parseTimeDaily } from '../../../../utils/time-utils';
import { LogGeolocation } from '../../../database/entities/log-geolocation.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { User } from '../../../database/entities/user.entity';
import { OrderDirection } from '../../../../dto/base-query.dto';
import {
  GeofenceAnalyticParams,
  GeofenceLog,
  GeofenceLogTransformed,
} from '../interfaces/geofence-analytic.interface';
import * as dayjs from 'dayjs';
import { Geofence } from '../../../database/entities/geofence.entity';

/**
 * Service for analyzing geofence entry/exit logs
 * Processes location data to track device movement through virtual boundaries
 */
@Injectable()
export class GeofenceAnalyticService {
  constructor(
    @InjectRepository(LogGeolocation)
    private readonly logGeolocationRepository: Repository<LogGeolocation>,
    @InjectRepository(Geofence)
    private readonly geofenceRepository: Repository<Geofence>,
  ) {}

  /**
   * Retrieves and analyzes geofence entry/exit logs
   *
   * @param parent_branch_id Parent branch identifier
   * @param params Filter parameters for date range, branch, zone, user, device, etc.
   * @param options Context information including timezone and user
   * @returns Structured geofence logs with duration and violation status
   */
  async getGeofenceLogs(
    parent_branch_id: string | number,
    params: GeofenceAnalyticParams,
    options: {
      timezone?: Timezone;
      user?: User;
    },
  ) {
    // Extract parameters with defaults
    const {
      startDate,
      endDate,
      startTime = '00:00:00',
      endTime = '23:59:59',
      branchId,
      zoneId,
      zoneLabels,
      roleId,
      userId,
      userLabels,
      deviceId,
      deviceLabels,
      page = 1,
      limit = 10,
      orderBy,
      orderDirection,
    } = params;

    // Validate timezone
    const timezone = options.timezone;
    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    // Build query for Common Table Expression (CTE) with row numbering
    const cteQuery = this.logGeolocationRepository
      .createQueryBuilder('log_geolocation')
      .select([
        'log_geolocation.uuid as uuid',
        'log_geolocation.branch_id as branch_id',
        'log_geolocation.device_id as device_id',
        'log_geolocation.device_name as device_name',
        'log_geolocation.user_id as user_id',
        'log_geolocation.user_name as user_name',
        'log_geolocation.zone_id as zone_id',
        'log_geolocation.zone_name as zone_name',
        'log_geolocation.geofence_id as geofence_id',
        'log_geolocation.geofence_name as geofence_name',
        'log_geolocation.active_time_start as active_time_start',
        'log_geolocation.active_time_end as active_time_end',
        'log_geolocation.maximum_stay_duration as maximum_stay_duration',
        'log_geolocation.minimum_stay_duration as minimum_stay_duration',
        'log_geolocation.latitude as latitude',
        'log_geolocation.longitude as longitude',
        'log_geolocation.original_submitted_time as original_submitted_time',
        // Row numbers for all logs and per-geofence logs (used for sequence calculation)
        'ROW_NUMBER() OVER (PARTITION BY log_geolocation.device_id ORDER BY log_geolocation.original_submitted_time ASC, log_geolocation.uuid) as rn_all',
        'ROW_NUMBER() OVER (PARTITION BY log_geolocation.device_id, log_geolocation.geofence_id ORDER BY log_geolocation.original_submitted_time ASC, log_geolocation.uuid) as rn_geo',
      ])
      .where('log_geolocation.parent_branch_id = :parentBranchId', {
        parentBranchId: parent_branch_id,
      });

    // Process date/time filters with timezone adjustment
    const rangeDateAndTimeDaily = parseTimeDaily({
      startDate,
      endDate,
      startTime,
      endTime,
      offset: timezone.gmt_offset,
    });

    // Apply date range filters using brackets for complex conditions
    if (rangeDateAndTimeDaily.length > 0) {
      cteQuery.andWhere(
        new Brackets(qb => {
          for (let i = 0; i < rangeDateAndTimeDaily.length; i++) {
            qb.orWhere(
              `log_geolocation.original_submitted_time >= :startDate${i}::timestamptz AND log_geolocation.original_submitted_time <= :endDate${i}::timestamptz`,
              {
                [`startDate${i}`]:
                  rangeDateAndTimeDaily[i].utc.startDate + '+00',
                [`endDate${i}`]: rangeDateAndTimeDaily[i].utc.endDate + '+00',
              },
            );
          }
        }),
      );
    }

    // Apply branch filter
    if (branchId) {
      cteQuery.andWhere('log_geolocation.branch_id = :branchId', {
        branchId,
      });
    }

    // Apply zone filters (direct ID or by labels)
    if (zoneId) {
      cteQuery.andWhere('log_geolocation.zone_id = :zoneId', {
        zoneId,
      });
    } else if (zoneLabels) {
      cteQuery
        .leftJoin('zone_labels', 'zl', 'zl.zone_id = log_geolocation.zone_id')
        .andWhere('zl.label_id IN (:...zoneLabels)', {
          zoneLabels,
        });
    }

    // Apply role filter
    if (roleId) {
      cteQuery.andWhere('log_geolocation.role_id = :roleId', {
        roleId,
      });
    }

    // Apply user filters (direct ID or by labels)
    if (userId) {
      cteQuery.andWhere('log_geolocation.user_id = :userId', {
        userId,
      });
    } else if (userLabels) {
      cteQuery
        .leftJoin('user_labels', 'ul', 'ul.user_id = log_geolocation.user_id')
        .andWhere('ul.label_id IN (:...userLabels)', {
          userLabels,
        });
    }

    // Apply device filters (direct ID or by labels)
    if (deviceId) {
      cteQuery.andWhere('log_geolocation.device_id = :deviceId', {
        deviceId,
      });
    } else if (deviceLabels) {
      cteQuery
        .leftJoin(
          'device_labels',
          'dl',
          'dl.device_id = log_geolocation.device_id',
        )
        .andWhere('dl.label_id IN (:...deviceLabels)', {
          deviceLabels,
        });
    }

    // Create subquery with sequence number calculation from row number difference
    const subQuery = this.logGeolocationRepository.manager
      .createQueryBuilder()
      .select([
        'all_logs.uuid',
        'all_logs.branch_id',
        'all_logs.device_id',
        'all_logs.device_name',
        'all_logs.user_id',
        'all_logs.user_name',
        'all_logs.zone_id',
        'all_logs.zone_name',
        'all_logs.geofence_id',
        'all_logs.geofence_name',
        'all_logs.active_time_start',
        'all_logs.active_time_end',
        'all_logs.maximum_stay_duration',
        'all_logs.minimum_stay_duration',
        'all_logs.latitude',
        'all_logs.longitude',
        'all_logs.original_submitted_time',
        // Calculate sequence number to identify entry/exit pairs
        '(all_logs.rn_all - all_logs.rn_geo) as seqnum',
      ])
      .from(`(${cteQuery.getQuery()})`, 'all_logs')
      .setParameters(cteQuery.getParameters())
      .where('all_logs.geofence_id IS NOT NULL');

    // Main query: Group by sequence to identify entry/exit pairs per device
    const mainQueryBuilder = this.logGeolocationRepository.manager
      .createQueryBuilder()
      .select([
        'MIN(subquery.branch_id) as branch_id',
        'MIN(subquery.geofence_id) as geofence_id',
        'MIN(subquery.geofence_name) as geofence_name',
        'MIN(subquery.device_id) as device_id',
        'MIN(subquery.device_name) as device_name',
        'MIN(subquery.user_id) as user_id',
        'MIN(subquery.user_name) as user_name',
        'MIN(subquery.zone_id) as zone_id',
        'MIN(subquery.zone_name) as zone_name',
        'MIN(subquery.maximum_stay_duration) as maximum_stay_duration',
        'MIN(subquery.minimum_stay_duration) as minimum_stay_duration',
        'MIN(subquery.original_submitted_time) as entry',
        'MAX(subquery.original_submitted_time) as exit',
        'subquery.seqnum',
      ])
      .from(`(${subQuery.getQuery()})`, 'subquery')
      .setParameters(subQuery.getParameters())
      .groupBy(['subquery.device_id', 'subquery.seqnum'].join(','));

    // Apply result ordering
    if (orderBy && orderDirection) {
      mainQueryBuilder.orderBy(
        `${orderBy === 'duration' ? 'duration' : orderBy}`,
        orderDirection === OrderDirection.ASC ? 'ASC' : 'DESC',
      );
    }

    // Count query for pagination
    const countQuery = this.logGeolocationRepository.manager
      .createQueryBuilder()
      .select('COUNT(*) as total')
      .from(`(${mainQueryBuilder.getQuery()})`, 'count_table')
      .setParameters(mainQueryBuilder.getParameters());

    // Get total count for pagination
    const totalResult = await countQuery.getRawOne();
    const total = parseInt(totalResult.total, 10);

    // Apply pagination limits
    const skip = (page - 1) * limit;
    mainQueryBuilder.offset(skip).limit(limit);

    // Execute main query
    const data: GeofenceLog[] = await mainQueryBuilder.getRawMany();

    // Get Timezones according to geofence_id -> branch_id -> timezone_id
    const geofenceIds = data.map(item => item.geofence_id);
    if (geofenceIds.length === 0) {
      return {
        data: [],
        total: 0,
      };
    }
    const geofenceTimezones = await this.geofenceRepository
      .createQueryBuilder('geofence')
      .select(['geofence.id', 'timezone.id', 'timezone.timezone_name'])
      .leftJoin('geofence.branch', 'branch')
      .leftJoin('branch.timezone', 'timezone')
      .where('geofence.id IN (:...geofenceIds)', { geofenceIds })
      .getRawMany();

    // Transform raw results to add duration and violation status
    const transformedData: GeofenceLogTransformed[] = data.map(item => {
      const findTimezone = geofenceTimezones.find(
        tz => parseInt(tz.geofence_id as any) === parseInt(item.geofence_id),
      );
      const duration = dayjs(item.exit).diff(dayjs(item.entry), 'seconds');
      const violation = this.isViolation(
        duration,
        item.maximum_stay_duration,
        item.minimum_stay_duration,
      );

      return {
        geofence_id: item.geofence_id,
        geofence_name: item.geofence_name,
        device_id: item.device_id,
        device_name: item.device_name,
        user_id: item.user_id,
        user_name: item.user_name,
        zone_id: item.zone_id,
        zone_name: item.zone_name,
        timezone_id: findTimezone!.timezone_id,
        timezone_name: findTimezone!.timezone_timezone_name,
        maximum_stay_duration: item.maximum_stay_duration,
        minimum_stay_duration: item.minimum_stay_duration,
        entry: item.entry,
        exit: item.exit,
        duration,
        violation,
      };
    });

    // Return formatted data with pagination metadata
    return {
      data: transformedData,
      total,
    };
  }

  /**
   * Checks if geofence stay violates duration constraints
   *
   * @param duration Actual stay duration in seconds
   * @param maximumStayDuration Maximum allowed time in format HH:mm:ss
   * @param minimumStayDuration Minimum required time in format HH:mm:ss
   * @returns True if stay duration violates constraints
   */
  private isViolation(
    duration: number,
    maximumStayDuration: string | null,
    minimumStayDuration: string | null,
  ): boolean {
    // Convert HH:mm:ss format to seconds for more precise comparison
    const convertTimeToSeconds = (timeString: string): number => {
      if (!timeString) return 0;
      const [hours, minutes, seconds] = timeString.split(':').map(Number);
      return hours * 3600 + minutes * 60 + seconds;
    };

    // Get duration limits in seconds
    const maxDurationSeconds = maximumStayDuration
      ? convertTimeToSeconds(maximumStayDuration)
      : null;
    const minDurationSeconds = minimumStayDuration
      ? convertTimeToSeconds(minimumStayDuration)
      : null;

    // Check for violations in seconds (more precise)
    if (maxDurationSeconds !== null && duration > maxDurationSeconds) {
      return true; // Violation - stayed too long
    }
    if (minDurationSeconds !== null && duration < minDurationSeconds) {
      return true; // Violation - stayed too short
    }

    return false; // No violation
  }
}
