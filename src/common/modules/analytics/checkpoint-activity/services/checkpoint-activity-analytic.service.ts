import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { LogCheckpoint } from '../../../database/entities/log-checkpoint.entity';
import { User } from '../../../database/entities/user.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { parseTimeDaily } from '../../../../utils/time-utils';
import { CheckpointActivityAnalyticParams } from '../interfaces/checkpoint-activity-analytic.interface';

@Injectable()
export class CheckpointActivityAnalyticService {
  constructor(
    @InjectRepository(LogCheckpoint)
    private readonly logCheckpointRepository: Repository<LogCheckpoint>,
    @InjectRepository(Timezone)
    private readonly timezoneRepository: Repository<Timezone>,
  ) {}

  /**
   * Retrieves checkpoint activity data based on specified filters and parameters
   * @param parent_branch_id The ID of the parent branch
   * @param params Query parameters for filtering
   * @param options Additional options like timezone and user info
   * @returns Object containing checkpoint activity logs and total count
   */
  async getCheckpointActivity(
    parent_branch_id: string | number,
    params: CheckpointActivityAnalyticParams,
    options: {
      timezone?: Timezone;
      user?: User;
    },
  ) {
    // Extract and set default values for query parameters
    const {
      startDate,
      endDate,
      startTime = '00:00:00',
      endTime = '23:59:59',
      branchId,
      zoneId,
      zoneLabels,
      checkpointId,
      checkpointLabels,
      roleId,
      userId,
      userLabels,
      deviceId,
      deviceLabels,
      page,
      limit,
      orderBy = 'original_submitted_time',
      orderDirection = 'DESC',
    } = params;

    const timezone = options.timezone;
    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    // Build base query with necessary joins
    const queryBuilder = this.logCheckpointRepository
      .createQueryBuilder('log_checkpoint')
      .leftJoinAndSelect('log_checkpoint.checkpoint', 'checkpoint')
      .leftJoinAndSelect('log_checkpoint.zone', 'zone')
      .leftJoinAndSelect('log_checkpoint.role', 'role')
      .leftJoinAndSelect('log_checkpoint.user', 'user')
      .leftJoinAndSelect('log_checkpoint.device', 'device')
      .leftJoinAndSelect('log_checkpoint.branch', 'branch')
      .leftJoinAndSelect('log_checkpoint.parent_branch', 'parent_branch')
      .leftJoinAndSelect('log_checkpoint.timezone', 'timezone')
      .leftJoinAndSelect('log_checkpoint.checkpoint_type', 'checkpoint_type');

    // Filter by parent branch ID (required)
    queryBuilder.where('log_checkpoint.parent_branch_id = :parentBranchId', {
      parentBranchId: parent_branch_id,
    });

    // Apply branch filter if provided
    if (branchId) {
      queryBuilder.andWhere('log_checkpoint.branch_id = :branchId', {
        branchId,
      });
    }

    // Zone/Site filtering
    if (zoneId) {
      queryBuilder.andWhere('log_checkpoint.zone_id = :zoneId', { zoneId });
    } else if (zoneLabels && zoneLabels.length > 0) {
      queryBuilder
        .leftJoin('zone_labels', 'zl', 'zl.zone_id = zone.id')
        .andWhere('zl.label_id IN (:...zoneLabels)', {
          zoneLabels,
        });
    }

    // Checkpoint filtering
    if (checkpointId) {
      queryBuilder.andWhere('log_checkpoint.checkpoint_id = :checkpointId', {
        checkpointId,
      });
    } else if (checkpointLabels && checkpointLabels.length > 0) {
      queryBuilder
        .leftJoin('checkpoint_labels', 'cl', 'cl.checkpoint_id = checkpoint.id')
        .andWhere('cl.label_id IN (:...checkpointLabels)', {
          checkpointLabels,
        });
    }

    // Role filtering
    if (roleId) {
      queryBuilder.andWhere('log_checkpoint.role_id = :roleId', { roleId });
    }

    // User filtering
    if (userId) {
      queryBuilder.andWhere('log_checkpoint.user_id = :userId', { userId });
    } else if (userLabels && userLabels.length > 0) {
      queryBuilder
        .leftJoin('user_labels', 'ul', 'ul.user_id = user.id')
        .andWhere('ul.label_id IN (:...userLabels)', {
          userLabels,
        });
    }

    // Device filtering
    if (deviceId) {
      queryBuilder.andWhere('log_checkpoint.device_id = :deviceId', {
        deviceId,
      });
    } else if (deviceLabels && deviceLabels.length > 0) {
      queryBuilder
        .leftJoin('device_labels', 'dl', 'dl.device_id = device.id')
        .andWhere('dl.label_id IN (:...deviceLabels)', {
          deviceLabels,
        });
    }

    // Parse date range with timezone adjustment
    const rangeDateAndTimeDaily = parseTimeDaily({
      startDate,
      endDate,
      startTime,
      endTime,
      offset: timezone.gmt_offset,
    });

    // Apply date filters using Brackets for complex OR conditions
    if (rangeDateAndTimeDaily.length > 0) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          for (let i = 0; i < rangeDateAndTimeDaily.length; i++) {
            qb.orWhere(
              `log_checkpoint.original_submitted_time >= :startDate${i}::timestamptz AND log_checkpoint.original_submitted_time <= :endDate${i}::timestamptz`,
              {
                [`startDate${i}`]:
                  rangeDateAndTimeDaily[i].utc.startDate + '+00',
                [`endDate${i}`]: rangeDateAndTimeDaily[i].utc.endDate + '+00',
              },
            );
          }
        }),
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`log_checkpoint.${orderBy}`, orderDirection);

    // Apply pagination
    if (page && limit) {
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);
    }

    // Execute query and get results with total count
    const [checkpointLogs, total] = await queryBuilder.getManyAndCount();

    // Return the results
    return {
      data: checkpointLogs,
      total,
    };
  }
}
